# Price Object Structure

## Overview
The price object in your system contains the following fields based on the Price entity and DTOs:

## Required Fields

### `currency` (string)
- **Description**: Currency code following ISO 4217 standard
- **Example**: `"usd"`, `"eur"`, `"gbp"`
- **Validation**: Required, must be a string

### `unitAmount` (number)
- **Description**: Price amount in the smallest currency unit (e.g., cents for USD)
- **Example**: `2000` (represents $20.00 USD)
- **Validation**: Required, must be a number

### `type` (string)
- **Description**: Type of pricing model
- **Options**: `"one_time"` or `"recurring"`
- **Example**: `"recurring"`
- **Validation**: Required, must be one of the enum values

## Optional Fields

### `nickname` (string)
- **Description**: Human-readable nickname for the price
- **Example**: `"Monthly Premium"`, `"Annual Discount"`
- **Default**: Empty string if not provided

### `active` (boolean)
- **Description**: Whether the price is active and can be used
- **Example**: `true` or `false`
- **Default**: `true`

### `interval` (string) - Required for recurring prices
- **Description**: Billing interval for recurring prices
- **Options**: `"day"`, `"week"`, `"month"`, `"year"`
- **Example**: `"month"`
- **Note**: Required when `type` is `"recurring"`

### `intervalCount` (number) - For recurring prices
- **Description**: Number of intervals between billings
- **Example**: `1` (every month), `3` (every 3 months)
- **Default**: `1`

### `trialPeriodDays` (number)
- **Description**: Number of days for trial period
- **Example**: `7`, `14`, `30`
- **Note**: Only applicable for recurring prices

### `usageType` (string) - For recurring prices
- **Description**: How the subscription is used
- **Options**: `"licensed"` or `"metered"`
- **Example**: `"licensed"`
- **Default**: `"licensed"`

### `stripePriceId` (string)
- **Description**: Stripe Price ID (auto-generated if not provided)
- **Example**: `"price_1NzV5gFkXjTxxxxxx"`
- **Note**: Will be created automatically in Stripe if not provided

## Example Price Objects

### One-time Payment
```json
{
  "currency": "usd",
  "unitAmount": 5000,
  "type": "one_time",
  "nickname": "Premium Course Access",
  "active": true
}
```

### Monthly Subscription
```json
{
  "currency": "usd",
  "unitAmount": 2000,
  "type": "recurring",
  "interval": "month",
  "intervalCount": 1,
  "nickname": "Monthly Premium",
  "active": true,
  "trialPeriodDays": 7,
  "usageType": "licensed"
}
```

### Annual Subscription with Discount
```json
{
  "currency": "usd",
  "unitAmount": 20000,
  "type": "recurring",
  "interval": "year",
  "intervalCount": 1,
  "nickname": "Annual Premium (Save 20%)",
  "active": true,
  "usageType": "licensed"
}
```

## Creating a Package with Prices

When creating a package, you can include multiple prices in the `prices` array:

```json
{
  "name": "Premium Plan",
  "description": "Full access to all features",
  "image": "https://example.com/premium-plan.png",
  "prices": [
    {
      "currency": "usd",
      "unitAmount": 2000,
      "type": "recurring",
      "interval": "month",
      "intervalCount": 1,
      "nickname": "Monthly Premium",
      "active": true,
      "trialPeriodDays": 7
    },
    {
      "currency": "usd",
      "unitAmount": 20000,
      "type": "recurring",
      "interval": "year",
      "intervalCount": 1,
      "nickname": "Annual Premium",
      "active": true
    }
  ]
}
```

## Database Fields (Read-only)

These fields are automatically managed by the system:

- `id`: UUID primary key
- `stripePriceId`: Stripe Price ID (auto-generated)
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp
- `package`: Reference to the associated package

## Validation Rules

1. **Currency**: Must be a valid ISO 4217 currency code
2. **Unit Amount**: Must be a positive integer
3. **Type**: Must be either "one_time" or "recurring"
4. **Interval**: Required when type is "recurring"
5. **Interval Count**: Must be positive integer if provided
6. **Trial Period Days**: Must be positive integer if provided
7. **Usage Type**: Must be "licensed" or "metered" if provided
