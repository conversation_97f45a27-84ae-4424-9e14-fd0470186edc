import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePackageDto {
  @ApiProperty({
    description: 'The name of the package',
    example: 'Premium Plan',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of the package',
    example: 'Premium subscription plan with all features',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Image URL for the package',
    example: 'https://example.com/package-image.png',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  image?: string;

  @ApiProperty({
    description: 'Stripe Product ID (optional, will be created if not provided)',
    example: 'prod_1234567890',
    required: false,
  })
  @IsOptional()
  @IsString()
  stripeProductId?: string;
}
