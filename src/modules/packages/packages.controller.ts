import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Delete,
  UseGuards
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { PackagesService } from './packages.service';
import { Roles } from '../auth/decorators/role.decorator';
import { EUserRole } from '../user/dto/create-user.dto';
import { RoleGuard } from '../auth/guards/role.guard';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { Package } from './entities/package.entity';
import { UserContext } from '../auth/services/rbac.service';

@ApiTags('Packages')
@Controller('packages')
@ApiBearerAuth()
export class PackagesController {
  constructor(private readonly packagesService: PackagesService) {}

  @Post()
  @UseGuards(RoleGuard)
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new package' })
  @ApiBody({ type: CreatePackageDto })
  @ApiResponse({
    status: 201,
    description: 'The package has been successfully created.',
    type: Package
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  create(@Body() createPackageDto: CreatePackageDto): Promise<Package> {
    return this.packagesService.create(createPackageDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all packages' })
  @ApiResponse({
    status: 200,
    description: 'Return all packages.',
    type: [Package]
  })
  findAll(): Promise<Package[]> {
    return this.packagesService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a package by ID' })
  @ApiParam({ name: 'id', description: 'Package ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the package.',
    type: Package
  })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  findOne(@Param('id') id: string): Promise<Package> {
    return this.packagesService.findOne(id);
  }

  @Put(':id')
  @UseGuards(RoleGuard)
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Update a package' })
  @ApiParam({ name: 'id', description: 'Package ID' })
  @ApiBody({ type: UpdatePackageDto })
  @ApiResponse({
    status: 200,
    description: 'The package has been successfully updated.',
    type: Package
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  update(
    @Param('id') id: string,
    @Body() updatePackageDto: UpdatePackageDto,
  ): Promise<Package> {
    return this.packagesService.update(id, updatePackageDto);
  }

  @Delete(':id')
  @UseGuards(RoleGuard)
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a package' })
  @ApiParam({ name: 'id', description: 'Package ID' })
  @ApiResponse({
    status: 200,
    description: 'The package has been successfully deleted.'
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  remove(@Param('id') id: string) {
    return this.packagesService.remove(id);
  }

  @Post('/subscription')
  @UseGuards(RoleGuard)
  @Roles(EUserRole.INDEPENDENT_TEACHER, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Create a subscription for a package' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'Package ID' }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Subscription checkout session created successfully.'
  })
  @ApiResponse({ status: 400, description: 'Bad request.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Package not found.' })
  subscription(@Body('id') id: string, @ActiveUser() user: UserContext) {
    const userId = user?.id;
    if (!userId) {
      throw new Error(`userId not found`);
    }
    return this.packagesService.onSubscription(userId, id);
  }
}
