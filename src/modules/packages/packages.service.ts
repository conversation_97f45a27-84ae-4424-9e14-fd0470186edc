import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import Stripe from 'stripe';
import { Repository } from 'typeorm';
import { StripeService } from '../stripe/stripe.service';
import { Package } from './entities/package.entity';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { Price } from '../price/entities/price.entity';

@Injectable()
export class PackagesService {
  constructor(
    @InjectRepository(Package)
    private packageRepository: Repository<Package>,
    @InjectRepository(Price)
    private priceRepository: Repository<Price>,
    private readonly stripeService: StripeService,
  ) {}

  async findAll() {
    return this.packageRepository.find({ relations: ['prices'] });
  }

  async findOne(id: string) {
    const packageEntity = await this.packageRepository.findOne({
      where: { id },
      relations: ['prices']
    });

    if (!packageEntity) {
      throw new NotFoundException(`Package with ID ${id} not found`);
    }

    return packageEntity;
  }

  async create(createPackageDto: CreatePackageDto) {
    const { prices, ...packageData } = createPackageDto;

    // If no stripeProductId provided, we'll create one via Stripe
    let stripeProductId = packageData.stripeProductId;

    if (!stripeProductId) {
      // Create product in Stripe first
      const stripeProduct = await this.stripeService.createProduct({
        name: packageData.name,
        description: packageData.description,
        images: packageData.image ? [packageData.image] : [],
      });
      stripeProductId = stripeProduct.id;
    }

    const finalPackageData = {
      ...packageData,
      stripeProductId,
      image: packageData.image || '', // Ensure image is not null
    };

    // Create the package first
    const newPackage = this.packageRepository.create(finalPackageData);
    const savedPackage = await this.packageRepository.save(newPackage);

    // Create prices if provided
    if (prices && prices.length > 0) {
      const createdPrices: Price[] = [];

      for (const priceData of prices) {
        // Create price in Stripe first
        const stripePrice = await this.stripeService.getStripeInstance().prices.create({
          currency: priceData.currency,
          unit_amount: priceData.unitAmount,
          product: stripeProductId,
          nickname: priceData.nickname,
          active: priceData.active ?? true,
          recurring: priceData.type === 'recurring' ? {
            interval: priceData.interval!,
            interval_count: priceData.intervalCount || 1,
            trial_period_days: priceData.trialPeriodDays,
            usage_type: priceData.usageType || 'licensed',
          } : undefined,
        });

        // Save price to database
        const price = this.priceRepository.create({
          stripePriceId: stripePrice.id,
          currency: priceData.currency,
          unitAmount: priceData.unitAmount,
          nickname: priceData.nickname || '',
          active: priceData.active ?? true,
          type: priceData.type,
          interval: priceData.interval,
          intervalCount: priceData.intervalCount,
          trialPeriodDays: priceData.trialPeriodDays,
          usageType: priceData.usageType,
          package: savedPackage,
        });

        const savedPrice = await this.priceRepository.save(price);
        createdPrices.push(savedPrice);
      }

      // Return package with prices
      return this.findOne(savedPackage.id);
    }

    return savedPackage;
  }

  async update(id: string, updatePackageDto: UpdatePackageDto) {
    const packageEntity = await this.findOne(id);

    // Update in Stripe if name or description changed
    if (updatePackageDto.name || updatePackageDto.description) {
      await this.stripeService.updateProduct(packageEntity.stripeProductId, {
        name: updatePackageDto.name || packageEntity.name,
        description: updatePackageDto.description || packageEntity.description,
        images: updatePackageDto.image ? [updatePackageDto.image] :
                packageEntity.image ? [packageEntity.image] : [],
      });
    }

    await this.packageRepository.update(id, updatePackageDto);
    return this.findOne(id);
  }

  async remove(id: string) {
    const packageEntity = await this.findOne(id);

    // Archive the product in Stripe instead of deleting
    await this.stripeService.updateProduct(packageEntity.stripeProductId, {
      active: false,
    });

    await this.packageRepository.softDelete(id);
    return { message: `Package with ID ${id} has been removed` };
  }

  async handleStripeProduct(product: Stripe.Product, eventType: string) {
    const existing = await this.packageRepository.findOne({
      where: { stripeProductId: product.id },
    });

    if (eventType === 'product.deleted') {
      if (existing) {
        // Option 1: soft delete nếu bạn dùng soft-delete
        // await this.repo.softRemove(existing);

        // Option 2: update thông tin là "deleted"
        existing.name = `[DELETED] ${existing.name}`;
        existing.stripeProductId = `${existing.stripeProductId}_deleted`;
        await this.packageRepository.save(existing);
      }
      return;
    }

    const packageData = {
      name: product.name,
      description: product.description ?? '',
      image: product.images?.[0] ?? '',
      stripeProductId: product.id,
    };

    if (existing) {
      await this.packageRepository.update(existing.id, packageData);
    } else {
      const newPackage = this.packageRepository.create(packageData);
      await this.packageRepository.save(newPackage);
    }
  }

  async onSubscription(userId: string, packageId: string) {
    if (!userId) {
      throw new BadRequestException('User ID is required');
    }

    const packageEntity = await this.packageRepository.findOne({
      where: { id: packageId },
      relations: ['prices'],
    });

    if (!packageEntity) {
      throw new NotFoundException(`Package with ID ${packageId} not found`);
    }

    const priceId = packageEntity.prices?.[0]?.id;
    if (!priceId) {
      throw new NotFoundException(
        `No active price found for package with ID ${packageId}`,
      );
    }

    return this.stripeService.createCheckoutSession({
      priceId,
      userId,
      packageId,
    });
  }
}
